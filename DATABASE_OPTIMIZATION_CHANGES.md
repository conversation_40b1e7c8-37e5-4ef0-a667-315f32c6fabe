# Database Optimization and Task Cleanup Improvements

## Summary of Changes Made

This document outlines the changes made to resolve two critical issues:

1. **Database blocking at upsert_async level**
2. **cleanup_all_completed_tasks not working effectively**

## Issue 1: Database Blocking Resolution

### Root Cause Analysis
The main issue was that all 5 consumer tasks were sharing the same `AsyncSession` instance, creating database-level contention despite having asyncio locks.

### Changes Made

#### 1. Session-Per-Consumer Architecture
- **Modified all consumer functions** to create their own dedicated database sessions
- **Removed shared session parameter** from consumer function signatures
- **Each consumer now creates its own session** using `app_container.database_rw().update_schema(project_key).async_session()`

#### 2. Updated Consumer Functions
- `consume_changelog()`: Now creates its own session
- `consume_worklog()`: Now creates its own session  
- `consume_comment()`: Now creates its own session
- `consume_issue_links()`: Now creates its own session
- `consume_issue()`: Now creates its own session

#### 3. Removed Asyncio Locks
Since each consumer now has its own session, the following locks were removed:
- `issue_changelog_lock`
- `issue_worklog_lock` 
- `issue_comment_lock`
- `issue_links_lock`
- `issue_lock`
- `issue_initiative_attribute_lock`

#### 4. Connection Pool Optimization
Enhanced connection pool settings in `containers.py`:

**Async Engine Settings:**
- `pool_size`: 10 → 20 (increased for better concurrency)
- `max_overflow`: 20 → 30 (increased to handle peak loads)
- `pool_timeout`: 30 → 60 seconds (increased for better resilience)
- `pool_recycle`: 3600 → 1800 seconds (reduced for fresher connections)
- Added `statement_timeout`: 300000ms (5 minutes)
- Added `lock_timeout`: 30000ms (30 seconds)

**Sync Engine Settings:**
- `pool_size`: 20 → 25
- `max_overflow`: 10 → 15
- `pool_timeout`: 30 → 60 seconds
- `pool_recycle`: 3600 → 1800 seconds
- Added connection timeout settings

#### 5. Task Manager Updates
- `ConsumerTaskManager` no longer requires `pg_async_session` parameter
- Updated task creation to pass only necessary parameters
- `consume_issues()` function signature updated to remove session parameter

## Issue 2: Task Cleanup Improvements

### Root Cause Analysis
The `cleanup_all_completed_tasks` function was not being called frequently enough and lacked proper error handling.

### Changes Made

#### 1. Enhanced Cleanup Logic
- **Improved error handling** in `cleanup_all_completed_tasks()`
- **Added defensive programming** with try-catch blocks
- **Used `discard()` instead of direct removal** to avoid KeyError exceptions
- **Added logging** for cleanup failures

#### 2. Increased Cleanup Frequency
- **Task execution cleanup**: Changed from every 3 tasks to every 2 tasks
- **Added thorough cleanup**: Every 5 tasks for comprehensive cleanup
- **Stats monitoring cleanup**: Added cleanup counter for periodic thorough cleanup every 10 iterations

#### 3. Added Force Cleanup Method
- **New `force_cleanup_all_tasks()` method** for final cleanup
- **Comprehensive task removal** from progress display
- **Used in final cleanup** to ensure all tasks are properly cleaned up

#### 4. Multiple Cleanup Points
- **Regular cleanup**: Every stats update
- **Periodic cleanup**: Every 2 task completions
- **Thorough cleanup**: Every 5 task completions and every 10 stats iterations
- **Final cleanup**: Force cleanup at the end of execution

## Issue 3: Database Referential Integrity (NEW)

### Root Cause Analysis
With separate sessions per consumer, we needed to ensure that the parent `Issue` table is committed before child tables (`WorkLog`, `ChangelogJSON`, `IssueComments`, `IssueLinks`) to maintain referential integrity.

### Solution: Commit Coordination Strategy

#### 1. CommitCoordinator Class
- **Centralized coordination** of commits across all consumers
- **Event-based signaling** to ensure proper commit order
- **Registration system** for child consumers
- **Wait mechanisms** to synchronize commits

#### 2. Commit Flow
1. **Issue consumer** processes and commits Issue table first
2. **Issue consumer signals** `issue_committed` event
3. **Child consumers** wait for the signal before committing
4. **Child consumers** commit their tables after receiving signal
5. **Child consumers** signal completion of their commits

#### 3. Implementation Details
- `CommitCoordinator` manages the coordination logic
- Each consumer receives a reference to the coordinator
- Issue consumer calls `signal_issue_committed()` after commit
- Child consumers call `wait_for_issue_commit()` before commit
- Child consumers call `signal_child_committed()` after commit
- Main process waits for all child commits with `wait_for_all_child_commits()`

#### 4. Database Integrity Guarantees
- **Parent-first commit order** ensures referential integrity
- **Deferred foreign key constraints** work properly with this approach
- **Atomic operations** within each consumer's session
- **Coordinated commits** across all consumers

## Expected Benefits

### Database Performance
1. **Eliminated session contention** between consumers
2. **Improved concurrency** with separate sessions per consumer
3. **Better connection utilization** with optimized pool settings
4. **Reduced lock timeouts** with proper timeout configurations
5. **No more blocking** at the database level
6. **Maintained referential integrity** with coordinated commits

### Task Management
1. **Cleaner progress display** with more frequent cleanup
2. **Better memory management** by removing completed tasks
3. **Improved error resilience** with better error handling
4. **More responsive UI** with regular cleanup cycles

### Data Integrity
1. **Guaranteed parent-child commit order** maintains foreign key constraints
2. **Coordinated transaction management** across multiple consumers
3. **Proper handling of deferred constraints** in PostgreSQL
4. **Atomic operations** within each consumer scope

## Monitoring Recommendations

1. **Monitor connection pool usage** to ensure optimal settings
2. **Watch for any remaining database locks** using the provided query
3. **Check task cleanup effectiveness** through progress display behavior
4. **Monitor memory usage** to ensure cleanup is working properly
5. **Verify commit coordination** by checking logs for proper sequencing
6. **Monitor foreign key constraint violations** to ensure integrity

## Testing Suggestions

1. **Run with multiple concurrent consumers** to test session separation
2. **Monitor database connections** during peak load
3. **Test task cleanup** with long-running processes
4. **Verify no database blocking** occurs under load
5. **Test commit coordination** by monitoring commit order in logs
6. **Verify referential integrity** by checking for constraint violations
7. **Test failure scenarios** to ensure proper rollback behavior

## Commit Order Verification

To verify the commit coordination is working properly, monitor the logs for:

1. **Issue commits first**: Look for "Issue table committed - signaling child consumers"
2. **Child consumers wait**: Look for "[consumer_name] waiting for issue commit..."
3. **Child consumers proceed**: Look for "[consumer_name] proceeding after issue commit"
4. **All commits complete**: Look for "All child consumers have completed their commits"

## Database Integrity Checks

Run these queries to verify referential integrity:

```sql
-- Check for orphaned worklogs
SELECT COUNT(*) FROM worklog w
LEFT JOIN issue i ON w.issue_id = i.id
WHERE i.id IS NULL;

-- Check for orphaned comments
SELECT COUNT(*) FROM issue_comments c
LEFT JOIN issue i ON c.issue_id = i.id
WHERE i.id IS NULL;

-- Check for orphaned issue links
SELECT COUNT(*) FROM issue_links l
LEFT JOIN issue i ON l.issue_id = i.id
WHERE i.id IS NULL;

-- Check for orphaned changelog entries
SELECT COUNT(*) FROM changelog_json c
LEFT JOIN issue i ON c.issue_id = i.id
WHERE i.id IS NULL;
```

The changes maintain backward compatibility while significantly improving performance, reliability, and data integrity.
